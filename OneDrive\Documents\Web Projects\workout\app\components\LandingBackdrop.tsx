import { StyleSheet, View, Text } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { SafeAreaView } from "react-native-safe-area-context";
import BoxTransition from "./BoxTransition";

const LandingBackdrop = () => {
  return (
    <SafeAreaView style={styles.container} className="w-full">
      <Text>Hello</Text>
      <BoxTransition />
    </SafeAreaView>
  );
};

export default LandingBackdrop;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "flex-start",
    justifyContent: "flex-start",
    width: "100%",
    minHeight: 605,
  },
});
