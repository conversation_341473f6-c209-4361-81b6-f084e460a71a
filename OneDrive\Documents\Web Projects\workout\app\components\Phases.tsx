import { Button, StyleSheet, Text, View } from "react-native";

const Phases = () => {
  return (
    <View className="flex items-start justify-start bg-secondary w-full">
      <Text className="font-roboto-thin text-primary" style={styles.date}>
        Week 2: Day 5
      </Text>
      <Text className="font-roboto-black text-primary" style={styles.phase}>
        Phase: 80%
      </Text>
      <Text
        className="font-roboto-medium text-2xl text-primary"
        style={styles.repRange}
      >
        Rep Range: 6-10
      </Text>
    </View>
  );
};

export default Phases;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  date: {
    fontSize: 20,
  },
  phase: {
    fontSize: 56,
    marginTop: -16,
  },
  repRange: {
    fontSize: 24,
    marginTop: -4,
  },
});
